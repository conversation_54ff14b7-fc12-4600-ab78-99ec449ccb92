[gd_resource type="Theme" load_steps=13 format=3 uid="uid://2g01d2kieu86"]

[ext_resource type="Texture2D" path="res://assets/useful_ui/advanced_buttons/layer_67.png" id="1_button_normal"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/advanced_buttons/layer_68.png" id="2_button_hover"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/advanced_buttons/layer_69.png" id="3_button_pressed"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/dialogue_panels/layer_32.png" id="4_panel"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/dialogue_panels/layer_33.png" id="5_dialogue_panel"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_87.png" id="6_frame"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/progress_bars/layer_27.png" id="7_progress_bg"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/progress_bars/layer_28.png" id="8_progress_fill"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_normal"]
texture = ExtResource("1_button_normal")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_hover"]
texture = ExtResource("2_button_hover")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_pressed"]
texture = ExtResource("3_button_pressed")
texture_margin_left = 12.0
texture_margin_top = 12.0
texture_margin_right = 12.0
texture_margin_bottom = 12.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_panel"]
texture = ExtResource("4_panel")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_dialogue_panel"]
texture = ExtResource("5_dialogue_panel")
texture_margin_left = 24.0
texture_margin_top = 24.0
texture_margin_right = 24.0
texture_margin_bottom = 24.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_frame"]
texture = ExtResource("6_frame")
texture_margin_left = 16.0
texture_margin_top = 16.0
texture_margin_right = 16.0
texture_margin_bottom = 16.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_progress_bg"]
texture = ExtResource("7_progress_bg")
texture_margin_left = 8.0
texture_margin_top = 8.0
texture_margin_right = 8.0
texture_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_progress_fill"]
texture = ExtResource("8_progress_fill")
texture_margin_left = 4.0
texture_margin_top = 4.0
texture_margin_right = 4.0
texture_margin_bottom = 4.0

[resource]
default_font_size = 20
Button/colors/font_color = Color(0.95, 0.9, 0.8, 1)
Button/colors/font_disabled_color = Color(0.5, 0.5, 0.5, 0.7)
Button/colors/font_hover_color = Color(1, 0.98, 0.9, 1)
Button/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
Button/colors/font_pressed_color = Color(0.85, 0.8, 0.7, 1)
Button/constants/outline_size = 3
Button/font_sizes/font_size = 20
Button/styles/disabled = SubResource("StyleBoxTexture_button_normal")
Button/styles/hover = SubResource("StyleBoxTexture_button_hover")
Button/styles/normal = SubResource("StyleBoxTexture_button_normal")
Button/styles/pressed = SubResource("StyleBoxTexture_button_pressed")
Label/colors/font_color = Color(0.95, 0.9, 0.8, 1)
Label/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
Label/colors/font_shadow_color = Color(0.15, 0.08, 0.03, 0.8)
Label/constants/outline_size = 2
Label/constants/shadow_offset_x = 2
Label/constants/shadow_offset_y = 2
Label/font_sizes/font_size = 20
LineEdit/colors/font_color = Color(0.95, 0.9, 0.8, 1)
LineEdit/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
LineEdit/colors/font_selected_color = Color(1, 1, 1, 1)
LineEdit/colors/selection_color = Color(0.6, 0.4, 0.2, 0.5)
LineEdit/constants/outline_size = 2
LineEdit/font_sizes/font_size = 18
Panel/styles/panel = SubResource("StyleBoxTexture_panel")
PanelContainer/styles/panel = SubResource("StyleBoxTexture_dialogue_panel")
RichTextLabel/colors/default_color = Color(0.95, 0.9, 0.8, 1)
RichTextLabel/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
RichTextLabel/colors/font_shadow_color = Color(0.15, 0.08, 0.03, 0.8)
RichTextLabel/constants/outline_size = 2
RichTextLabel/constants/shadow_offset_x = 1
RichTextLabel/constants/shadow_offset_y = 1
RichTextLabel/font_sizes/normal_font_size = 18
ProgressBar/styles/background = SubResource("StyleBoxTexture_progress_bg")
ProgressBar/styles/fill = SubResource("StyleBoxTexture_progress_fill")
MarginContainer/styles/panel = SubResource("StyleBoxTexture_frame")
