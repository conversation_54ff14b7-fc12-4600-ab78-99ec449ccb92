[gd_scene load_steps=12 format=3 uid="uid://bqxvn8ywqhqxr"]

[ext_resource type="Script" path="res://scripts/EnhancedMainMenu.gd" id="1_script"]
[ext_resource type="Theme" uid="uid://2g01d2kieu86" path="res://themes/GothicTheme.tres" id="2_theme"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_87.png" id="3_main_frame"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_88.png" id="4_title_frame"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_89.png" id="5_menu_frame"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="6_background"]
[ext_resource type="Texture2D" path="res://assets/logo.png" id="7_logo"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_main_frame"]
texture = ExtResource("3_main_frame")
texture_margin_left = 32.0
texture_margin_top = 32.0
texture_margin_right = 32.0
texture_margin_bottom = 32.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_title_frame"]
texture = ExtResource("4_title_frame")
texture_margin_left = 24.0
texture_margin_top = 24.0
texture_margin_right = 24.0
texture_margin_bottom = 24.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_menu_frame"]
texture = ExtResource("5_menu_frame")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[sub_resource type="Animation" id="Animation_fade_in"]
resource_name = "fade_in"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[node name="EnhancedMainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("2_theme")
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("6_background")
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 40
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 40
theme_override_constants/margin_bottom = 40
theme_override_styles/panel = SubResource("StyleBoxTexture_main_frame")

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 30

[node name="TitleSection" type="MarginContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20
theme_override_styles/panel = SubResource("StyleBoxTexture_title_frame")

[node name="TitleVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="LogoContainer" type="CenterContainer" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2

[node name="Logo" type="TextureRect" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox/LogoContainer"]
layout_mode = 2
texture = ExtResource("7_logo")
stretch_mode = 4

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "PREKLIATE DEDIČSTVO"
horizontal_alignment = 1

[node name="SubtitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Van Helsing: Gotická dobrodružná hra"
horizontal_alignment = 1

[node name="VersionLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_font_sizes/font_size = 16
text = "verzia 1.0.0"
horizontal_alignment = 1

[node name="MenuSection" type="MarginContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20
theme_override_styles/panel = SubResource("StyleBoxTexture_menu_frame")

[node name="MenuVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection"]
layout_mode = 2
theme_override_constants/separation = 25

[node name="ButtonsContainer" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_constants/separation = 20

[node name="NovaHraButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "🆕 Nová Hra"

[node name="PokracovatButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "▶️ Pokračovať"

[node name="KapitolyButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "📚 Kapitoly"

[node name="NastaveniaButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "⚙️ Nastavenia"

[node name="OHreButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "ℹ️ O Hre"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("Animation_fade_in")
}
