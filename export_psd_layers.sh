#!/bin/bash

# Script na export vrstiev z PSD súborov
# Použitie: ./export_psd_layers.sh

PSD_DIR="Prekliate_dedicstvo_BACKUP_20250701_0240/assets/craftpix-net-699601-rpg-mmo-ui/PSD Files"
OUTPUT_DIR="exported_ui_elements"

# Vytvor výstupný adresár
mkdir -p "$OUTPUT_DIR"

echo "🎨 Exportujem UI elementy z PSD súborov..."

# Funkcia na export vrstiev z PSD súboru
export_layers() {
    local psd_file="$1"
    local psd_name="$2"
    local max_layers="$3"
    
    echo "📁 Exportujem z: $psd_name"
    mkdir -p "$OUTPUT_DIR/$psd_name"
    
    # Export prvých 50 vrstiev (aby sme nezahltili systém)
    for i in $(seq 0 49); do
        if [ $i -lt $max_layers ]; then
            echo "  🔄 Exportujem vrstvu $i..."
            magick "$psd_file[$i]" "$OUTPUT_DIR/$psd_name/layer_${i}.png" 2>/dev/null
            
            # Skontroluj, či sa vytvoril súbor a má rozumnú veľkosť
            if [ -f "$OUTPUT_DIR/$psd_name/layer_${i}.png" ]; then
                size=$(stat -f%z "$OUTPUT_DIR/$psd_name/layer_${i}.png" 2>/dev/null || echo 0)
                if [ $size -lt 1000 ]; then
                    # Zmaž príliš malé súbory (pravdepodobne prázdne vrstvy)
                    rm "$OUTPUT_DIR/$psd_name/layer_${i}.png"
                else
                    echo "    ✅ Vrstva $i exportovaná (${size} bytes)"
                fi
            fi
        fi
    done
}

# Export z hlavného PSD súboru
export_layers "$PSD_DIR/RPG & MMO 5.psd" "main_ui" 995

# Export z HUD PSD súboru  
export_layers "$PSD_DIR/RPG & MMO 5 - HUD.psd" "hud_ui" 1716

# Export z MOBILE PSD súboru
export_layers "$PSD_DIR/RPG & MMO 5 - MOBILE.psd" "mobile_ui" 245

echo "🎉 Export dokončený! Skontroluj zložku: $OUTPUT_DIR"
echo "📊 Štatistiky:"
find "$OUTPUT_DIR" -name "*.png" | wc -l | xargs echo "   Exportované súbory:"
du -sh "$OUTPUT_DIR" | cut -f1 | xargs echo "   Celková veľkosť:"
